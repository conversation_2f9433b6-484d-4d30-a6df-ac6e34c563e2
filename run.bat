@echo off
echo Hypixel SkyBlock Auction Tracker
echo =================================

REM Check if Rust is installed
cargo --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Rust not found. Please install Rust from https://rustup.rs/
    echo After installation, restart your command prompt and run this script again.
    pause
    exit /b 1
)

echo Rust is installed!
echo.

REM Build the project
echo Building project...
cargo build --release
if %errorlevel% neq 0 (
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo Build successful!
echo.

REM Set environment variable for logging
set RUST_LOG=info

echo Starting Hypixel Auction Tracker...
echo Press Ctrl+C to stop the application and save data.
echo Data will be saved to auction_data.json
echo.

REM Run the application
cargo run --release

echo.
echo Application stopped.
pause
