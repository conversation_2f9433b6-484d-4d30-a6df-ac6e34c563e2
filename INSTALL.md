# Installation Guide

## Quick Start

### Step 1: Install Rust
1. Go to [https://rustup.rs/](https://rustup.rs/)
2. Download and run the installer
3. Follow the installation instructions
4. **Restart your command prompt/terminal** after installation

### Step 2: Run the Application
1. Open Command Prompt (cmd) or PowerShell
2. Navigate to this directory:
   ```
   cd "C:\Users\<USER>\OneDrive\Desktop\Pheonix Flipper\hypixel-auction-tracker"
   ```
3. Run the batch file:
   ```
   run.bat
   ```

## Alternative Manual Method

If the batch file doesn't work:

1. Open Command Prompt in this directory
2. Build the project:
   ```
   cargo build --release
   ```
3. Run the application:
   ```
   cargo run --release
   ```

## Troubleshooting

### "cargo is not recognized"
- Rust is not installed or not in PATH
- Install Rust from https://rustup.rs/
- Restart your terminal after installation

### Build Errors
- Make sure you have an internet connection (Rust needs to download dependencies)
- Try running: `cargo clean` then `cargo build --release`

### No Data Being Saved
- The application needs to run for a few minutes to collect data
- Check that `auction_data.json` is created in the same directory
- Look for log messages about "Processed X ended auctions, saved Y sales"

## Expected Behavior

When running correctly, you should see:
```
[INFO] Starting Hypixel SkyBlock Auction Tracker
[INFO] No existing database file found, starting fresh
[INFO] Checking for auction updates...
[INFO] API updated! New timestamp: 1752045515520
[INFO] Fetched 1234 auctions from 25 pages
[INFO] Active auctions: 1234 total (1234 new, 0 updated)
[INFO] Fetched 156 ended auctions
[INFO] Processed 156 ended auctions, saved 142 sales
[INFO] Saved database with 89 items to auction_data.json
```

The key indicator that it's working is seeing "saved X sales" messages.

## Output File

The application creates `auction_data.json` with this format:
```json
{
  "item-name-attributes-tier": [
    {
      "sale_price": 1000000,
      "time_listed": 1752045515361,
      "time_sold": 1752045615361
    }
  ]
}
```

## Performance

- Checks every 59 seconds for new data
- Uses 100ms retry logic when waiting for API updates
- Saves data automatically when auctions end
- Handles Ctrl+C gracefully to save data before exit
