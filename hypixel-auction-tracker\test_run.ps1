# PowerShell script to test the Hypixel Auction Tracker

Write-Host "Testing Hypixel SkyBlock Auction Tracker" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Check if Rust is installed
try {
    $rustVersion = cargo --version
    Write-Host "Rust version: $rustVersion" -ForegroundColor Green
} catch {
    Write-Host "Rust not found. Please run install_rust.ps1 first." -ForegroundColor Red
    exit 1
}

# Build the project
Write-Host "`nBuilding project..." -ForegroundColor Blue
try {
    cargo build --release
    Write-Host "Build successful!" -ForegroundColor Green
} catch {
    Write-Host "Build failed. Please check the error messages above." -ForegroundColor Red
    exit 1
}

# Set environment variables for testing
$env:RUST_LOG = "info"

Write-Host "`nStarting auction tracker..." -ForegroundColor Blue
Write-Host "This will run for 5 minutes to test functionality." -ForegroundColor Yellow
Write-Host "Watch for messages about fetching and processing auctions." -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop early." -ForegroundColor Yellow
Write-Host "`n" -ForegroundColor White

# Start the application with a timeout
$job = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    $env:RUST_LOG = "info"
    cargo run --release
}

# Wait for 5 minutes or until user presses Ctrl+C
$timeout = 300 # 5 minutes
$elapsed = 0

try {
    while ($elapsed -lt $timeout -and $job.State -eq "Running") {
        Start-Sleep -Seconds 1
        $elapsed++
        
        # Show progress every 30 seconds
        if ($elapsed % 30 -eq 0) {
            Write-Host "Running for $elapsed seconds..." -ForegroundColor Blue
        }
    }
} finally {
    # Stop the job
    Stop-Job -Job $job -ErrorAction SilentlyContinue
    Remove-Job -Job $job -ErrorAction SilentlyContinue
}

Write-Host "`nTest completed!" -ForegroundColor Green

# Check if auction_data.json was created
if (Test-Path "auction_data.json") {
    Write-Host "✓ auction_data.json file was created" -ForegroundColor Green
    
    # Show file size and basic info
    $fileInfo = Get-Item "auction_data.json"
    Write-Host "  File size: $($fileInfo.Length) bytes" -ForegroundColor Blue
    
    # Try to parse and show basic stats
    try {
        $jsonContent = Get-Content "auction_data.json" | ConvertFrom-Json
        $itemCount = ($jsonContent | Get-Member -MemberType NoteProperty).Count
        Write-Host "  Items tracked: $itemCount" -ForegroundColor Blue
        
        if ($itemCount -gt 0) {
            Write-Host "✓ Application is working correctly!" -ForegroundColor Green
            Write-Host "`nSample items:" -ForegroundColor Yellow
            ($jsonContent | Get-Member -MemberType NoteProperty | Select-Object -First 3).Name | ForEach-Object {
                Write-Host "  - $_" -ForegroundColor Cyan
            }
        } else {
            Write-Host "⚠ No items were saved yet. This might be normal if no auctions ended during the test." -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠ Could not parse JSON file, but it exists." -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ auction_data.json file was not created" -ForegroundColor Yellow
    Write-Host "  This might be normal if no auctions ended during the test period." -ForegroundColor Yellow
}

Write-Host "`nTo run the full application: cargo run --release" -ForegroundColor Blue
