{"rustc": 1842507548689473721, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 17991074870192849766, "deps": [[555019317135488525, "regex_automata", false, 13945818237861117518], [2779309023524819297, "aho_corasick", false, 14086489310211949954], [9408802513701742484, "regex_syntax", false, 3239505838181348365], [15932120279885307830, "memchr", false, 16891707318405355952]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-ca5185afcd42585a\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}