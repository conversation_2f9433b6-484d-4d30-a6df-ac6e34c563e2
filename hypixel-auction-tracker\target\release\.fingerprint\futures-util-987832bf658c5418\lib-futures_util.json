{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 3867004589505629659, "deps": [[1615478164327904835, "pin_utils", false, 8759891987286999773], [1906322745568073236, "pin_project_lite", false, 11757906601842327119], [7620660491849607393, "futures_core", false, 3974386126593812265], [16240732885093539806, "futures_task", false, 15254173311372150526]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-987832bf658c5418\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}