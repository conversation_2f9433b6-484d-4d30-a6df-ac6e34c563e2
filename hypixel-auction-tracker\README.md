# Hypixel SkyBlock Auction Tracker

A Rust application that monitors the Hypixel SkyBlock auction house and tracks item sales data.

## Features

- **Real-time Monitoring**: Checks the auction house every 59 seconds
- **Smart Updates**: Only processes new data when the API timestamp updates
- **Retry Logic**: Checks every 100ms until new data is available
- **Item Tracking**: Creates unique keys for items based on name and attributes
- **Sale Recording**: Tracks sale price, listing time, and sale time for each item
- **JSON Output**: Saves data in a structured JSON format
- **Persistent Storage**: Loads existing data on startup and saves on shutdown

## Prerequisites

1. **Install Rust**: Download and install Rust from [https://rustup.rs/](https://rustup.rs/)
2. **Verify Installation**: Run `cargo --version` to confirm Rust is installed

## Installation

1. Navigate to the project directory:
   ```bash
   cd hypixel-auction-tracker
   ```

2. Build the project:
   ```bash
   cargo build --release
   ```

## Usage

1. Run the application:
   ```bash
   cargo run
   ```

2. The application will:
   - Start monitoring the Hypixel SkyBlock auction house
   - Check for updates every 59 seconds
   - Process new auctions and ended auctions
   - Save data to `auction_data.json`

3. To stop the application, press `Ctrl+C`. The application will save the database before shutting down.

## Output Format

The application creates an `auction_data.json` file with the following structure:

```json
{
  "item-name-attribute1-attribute2-tierN": [
    {
      "sale_price": 1000000,
      "time_listed": 1752045515361,
      "time_sold": 1752045615361
    }
  ]
}
```

### Item Key Format

Item keys are generated in the format: `itemname-attribute1-...-attributeN`

Attributes include:
- **Stats**: strength-X, defense-X, health-X, intelligence-X, crit-damage-X, crit-chance-X
- **Tier**: tier-common, tier-uncommon, tier-rare, tier-epic, tier-legendary, tier-mythic

Example: `Aspect of the End-strength-100-crit-damage-50-tier-epic`

## Configuration

### Environment Variables

- `RUST_LOG`: Set logging level (e.g., `RUST_LOG=info cargo run`)

### Logging Levels

- `error`: Only errors
- `warn`: Warnings and errors
- `info`: General information (default)
- `debug`: Detailed debugging information
- `trace`: Very detailed tracing

## API Information

This application uses the official Hypixel API:
- **Active Auctions**: `https://api.hypixel.net/v2/skyblock/auctions`
- **Ended Auctions**: `https://api.hypixel.net/v2/skyblock/auctions_ended`

The application respects API rate limits and includes delays between requests.

## Data Persistence

- **Startup**: Loads existing data from `auction_data.json` if available
- **Runtime**: Continuously updates the in-memory database
- **Shutdown**: Saves all data to `auction_data.json`
- **Graceful Shutdown**: Handles Ctrl+C to ensure data is saved

## Error Handling

The application includes comprehensive error handling for:
- Network connectivity issues
- API response parsing errors
- File I/O operations
- Invalid data formats

Errors are logged but don't stop the application from continuing to monitor.

## Performance

- **Memory Efficient**: Only keeps active auctions in memory
- **Network Optimized**: Includes delays between API calls
- **Incremental Updates**: Only processes new data based on timestamps

## Troubleshooting

### Common Issues

1. **Rust not installed**: Install Rust from [https://rustup.rs/](https://rustup.rs/)
2. **Build errors**: Run `cargo clean` then `cargo build --release`
3. **Network errors**: Check internet connection and Hypixel API status
4. **Permission errors**: Ensure write permissions in the application directory

### Debug Mode

Run with debug logging to see detailed information:
```bash
RUST_LOG=debug cargo run
```

## License

This project is for educational purposes. Please respect Hypixel's API terms of service.
