# PowerShell script to install Rust and run the Hypixel Auction Tracker

Write-Host "Hypixel SkyBlock Auction Tracker - Installation Script" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green

# Check if Rust is already installed
try {
    $rustVersion = cargo --version 2>$null
    if ($rustVersion) {
        Write-Host "Rust is already installed: $rustVersion" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Rust not found. Installing Rust..." -ForegroundColor Yellow
    
    # Download and install Rust
    Write-Host "Downloading Rust installer..." -ForegroundColor Blue
    $rustupUrl = "https://win.rustup.rs/x86_64"
    $rustupPath = "$env:TEMP\rustup-init.exe"
    
    try {
        Invoke-WebRequest -Uri $rustupUrl -OutFile $rustupPath
        Write-Host "Running Rust installer..." -ForegroundColor Blue
        Start-Process -FilePath $rustupPath -ArgumentList "-y" -Wait
        
        # Refresh environment variables
        $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH", "User")
        
        Write-Host "Rust installation completed!" -ForegroundColor Green
    } catch {
        Write-Host "Failed to install Rust. Please install manually from https://rustup.rs/" -ForegroundColor Red
        exit 1
    }
}

# Verify Rust installation
try {
    $rustVersion = cargo --version
    Write-Host "Rust version: $rustVersion" -ForegroundColor Green
} catch {
    Write-Host "Rust installation failed. Please restart your terminal and try again." -ForegroundColor Red
    Write-Host "Or install manually from https://rustup.rs/" -ForegroundColor Red
    exit 1
}

# Build the project
Write-Host "`nBuilding Hypixel Auction Tracker..." -ForegroundColor Blue
try {
    cargo build --release
    Write-Host "Build completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Build failed. Please check the error messages above." -ForegroundColor Red
    exit 1
}

# Ask user if they want to run the application
Write-Host "`nWould you like to run the Hypixel Auction Tracker now? (y/n): " -ForegroundColor Yellow -NoNewline
$response = Read-Host

if ($response -eq "y" -or $response -eq "Y" -or $response -eq "yes" -or $response -eq "Yes") {
    Write-Host "`nStarting Hypixel Auction Tracker..." -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop the application and save data." -ForegroundColor Yellow
    Write-Host "Data will be saved to auction_data.json" -ForegroundColor Yellow
    Write-Host "`n" -ForegroundColor White
    
    # Set logging level to info
    $env:RUST_LOG = "info"
    
    # Run the application
    cargo run --release
} else {
    Write-Host "`nTo run the application later, use: cargo run --release" -ForegroundColor Blue
    Write-Host "Or with debug logging: RUST_LOG=debug cargo run --release" -ForegroundColor Blue
}

Write-Host "`nInstallation complete!" -ForegroundColor Green
