use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use log::{error, info, warn};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::time::Duration;
use tokio::time::{interval, sleep};

// Data structures for Hypixel API response
#[derive(Debug, Deserialize)]
struct AuctionResponse {
    success: bool,
    page: u32,
    #[serde(rename = "totalPages")]
    total_pages: u32,
    #[serde(rename = "totalAuctions")]
    total_auctions: u32,
    #[serde(rename = "lastUpdated")]
    last_updated: u64,
    auctions: Vec<Auction>,
}

#[derive(Debug, Deserialize, Clone)]
struct Auction {
    uuid: String,
    auctioneer: String,
    #[serde(rename = "profile_id")]
    profile_id: String,
    coop: Vec<String>,
    start: u64,
    end: u64,
    #[serde(rename = "item_name")]
    item_name: String,
    #[serde(rename = "item_lore")]
    item_lore: String,
    extra: String,
    category: String,
    tier: String,
    #[serde(rename = "starting_bid")]
    starting_bid: u64,
    #[serde(rename = "item_bytes")]
    item_bytes: String,
    claimed: bool,
    #[serde(rename = "claimed_bidders")]
    claimed_bidders: Vec<String>,
    #[serde(rename = "highest_bid_amount")]
    highest_bid_amount: u64,
    #[serde(rename = "last_updated")]
    last_updated: u64,
    bin: bool,
    bids: Vec<Bid>,
    #[serde(rename = "item_uuid")]
    item_uuid: Option<String>,
}

#[derive(Debug, Deserialize, Clone)]
struct Bid {
    #[serde(rename = "auction_id")]
    auction_id: String,
    bidder: String,
    #[serde(rename = "profile_id")]
    profile_id: String,
    amount: u64,
    timestamp: u64,
}

// Data structures for ended auctions
#[derive(Debug, Deserialize)]
struct EndedAuctionResponse {
    success: bool,
    #[serde(rename = "lastUpdated")]
    last_updated: u64,
    auctions: Vec<EndedAuction>,
}

#[derive(Debug, Deserialize, Clone)]
struct EndedAuction {
    #[serde(rename = "auction_id")]
    auction_id: String,
    seller: String,
    #[serde(rename = "seller_profile")]
    seller_profile: String,
    buyer: String,
    #[serde(rename = "buyer_profile")]
    buyer_profile: String,
    timestamp: u64,
    price: u64,
    bin: bool,
    #[serde(rename = "item_bytes")]
    item_bytes: String,
}

// Output data structure
#[derive(Debug, Serialize, Deserialize, Clone)]
struct ItemSaleData {
    sale_price: u64,
    time_listed: u64,
    time_sold: u64,
}

type AuctionDatabase = HashMap<String, Vec<ItemSaleData>>;

struct AuctionTracker {
    client: Client,
    database: AuctionDatabase,
    last_timestamp: u64,
    active_auctions: HashMap<String, Auction>,
}

impl AuctionTracker {
    fn new() -> Self {
        Self {
            client: Client::new(),
            database: HashMap::new(),
            last_timestamp: 0,
            active_auctions: HashMap::new(),
        }
    }

    // Generate item key from auction data
    fn generate_item_key(&self, auction: &Auction) -> String {
        let mut key_parts = vec![auction.item_name.clone()];

        // Extract attributes from item_lore
        let attributes = self.extract_attributes(&auction.item_lore, auction);
        key_parts.extend(attributes);

        key_parts.join("-")
    }

    // Extract attributes from item lore
    fn extract_attributes(&self, lore: &str, auction: &Auction) -> Vec<String> {
        let mut attributes = Vec::new();
        
        // Parse common attributes from lore
        for line in lore.lines() {
            let clean_line = line.replace("§", "").replace("§", "");
            
            // Look for common attribute patterns
            if clean_line.contains("Strength:") {
                if let Some(value) = self.extract_stat_value(&clean_line, "Strength:") {
                    attributes.push(format!("strength-{}", value));
                }
            }
            if clean_line.contains("Defense:") {
                if let Some(value) = self.extract_stat_value(&clean_line, "Defense:") {
                    attributes.push(format!("defense-{}", value));
                }
            }
            if clean_line.contains("Health:") {
                if let Some(value) = self.extract_stat_value(&clean_line, "Health:") {
                    attributes.push(format!("health-{}", value));
                }
            }
            if clean_line.contains("Intelligence:") {
                if let Some(value) = self.extract_stat_value(&clean_line, "Intelligence:") {
                    attributes.push(format!("intelligence-{}", value));
                }
            }
            if clean_line.contains("Crit Damage:") {
                if let Some(value) = self.extract_stat_value(&clean_line, "Crit Damage:") {
                    attributes.push(format!("crit-damage-{}", value));
                }
            }
            if clean_line.contains("Crit Chance:") {
                if let Some(value) = self.extract_stat_value(&clean_line, "Crit Chance:") {
                    attributes.push(format!("crit-chance-{}", value));
                }
            }
        }
        
        // Add tier as attribute
        attributes.push(format!("tier-{}", auction.tier.to_lowercase()));
        
        attributes
    }

    // Extract numeric value from stat line
    fn extract_stat_value(&self, line: &str, stat_name: &str) -> Option<String> {
        if let Some(pos) = line.find(stat_name) {
            let after_stat = &line[pos + stat_name.len()..];
            // Extract the first number found
            let mut number = String::new();
            let mut found_digit = false;
            
            for ch in after_stat.chars() {
                if ch.is_ascii_digit() || (ch == '.' && found_digit) {
                    number.push(ch);
                    found_digit = true;
                } else if found_digit {
                    break;
                }
            }
            
            if !number.is_empty() {
                return Some(number);
            }
        }
        None
    }

    // Fetch active auctions from API
    async fn fetch_active_auctions(&self) -> Result<Vec<Auction>> {
        let mut all_auctions = Vec::new();
        let mut page = 0;

        loop {
            let url = format!("https://api.hypixel.net/v2/skyblock/auctions?page={}", page);

            let response = self.client
                .get(&url)
                .send()
                .await
                .context("Failed to send request to Hypixel API")?;

            let auction_response: AuctionResponse = response
                .json()
                .await
                .context("Failed to parse auction response")?;

            if !auction_response.success {
                return Err(anyhow::anyhow!("API returned success: false"));
            }

            // Check if timestamp is newer than our last update
            if page == 0 && auction_response.last_updated <= self.last_timestamp {
                info!("No new auction data available (timestamp: {})", auction_response.last_updated);
                return Ok(Vec::new());
            }

            all_auctions.extend(auction_response.auctions);

            page += 1;
            if page >= auction_response.total_pages {
                break;
            }

            // Small delay between pages to be respectful to the API
            sleep(Duration::from_millis(50)).await;
        }

        info!("Fetched {} auctions from {} pages", all_auctions.len(), page);
        Ok(all_auctions)
    }

    // Fetch ended auctions from API
    async fn fetch_ended_auctions(&self) -> Result<Vec<EndedAuction>> {
        let url = "https://api.hypixel.net/v2/skyblock/auctions_ended";

        let response = self.client
            .get(url)
            .send()
            .await
            .context("Failed to send request to ended auctions API")?;

        let ended_response: EndedAuctionResponse = response
            .json()
            .await
            .context("Failed to parse ended auction response")?;

        if !ended_response.success {
            return Err(anyhow::anyhow!("Ended auctions API returned success: false"));
        }

        info!("Fetched {} ended auctions", ended_response.auctions.len());
        Ok(ended_response.auctions)
    }

    // Check if API has updated data with retry logic
    async fn wait_for_api_update(&mut self) -> Result<u64> {
        loop {
            let url = "https://api.hypixel.net/v2/skyblock/auctions?page=0";

            let response = self.client
                .get(url)
                .send()
                .await
                .context("Failed to check API timestamp")?;

            let auction_response: AuctionResponse = response
                .json()
                .await
                .context("Failed to parse timestamp check response")?;

            if !auction_response.success {
                return Err(anyhow::anyhow!("API returned success: false during timestamp check"));
            }

            if auction_response.last_updated > self.last_timestamp {
                info!("API updated! New timestamp: {}", auction_response.last_updated);
                self.last_timestamp = auction_response.last_updated;
                return Ok(auction_response.last_updated);
            }

            info!("API not updated yet, waiting 100ms...");
            sleep(Duration::from_millis(100)).await;
        }
    }

    // Process active auctions and update tracking
    async fn process_active_auctions(&mut self, auctions: Vec<Auction>) -> Result<()> {
        for auction in auctions {
            self.active_auctions.insert(auction.uuid.clone(), auction);
        }

        info!("Tracking {} active auctions", self.active_auctions.len());
        Ok(())
    }

    // Process ended auctions and update database
    async fn process_ended_auctions(&mut self, ended_auctions: Vec<EndedAuction>) -> Result<()> {
        for ended_auction in ended_auctions {
            // Find the corresponding active auction
            if let Some(active_auction) = self.active_auctions.remove(&ended_auction.auction_id) {
                let item_key = self.generate_item_key(&active_auction);

                let sale_data = ItemSaleData {
                    sale_price: ended_auction.price,
                    time_listed: active_auction.start,
                    time_sold: ended_auction.timestamp,
                };

                self.database
                    .entry(item_key.clone())
                    .or_insert_with(Vec::new)
                    .push(sale_data);

                info!("Recorded sale for {}: {} coins", item_key, ended_auction.price);
            }
        }

        Ok(())
    }

    // Save database to JSON file
    async fn save_database(&self) -> Result<()> {
        let json_data = serde_json::to_string_pretty(&self.database)
            .context("Failed to serialize database to JSON")?;

        fs::write("auction_data.json", json_data)
            .context("Failed to write JSON file")?;

        info!("Saved database with {} items to auction_data.json", self.database.len());
        Ok(())
    }

    // Load database from JSON file
    async fn load_database(&mut self) -> Result<()> {
        match fs::read_to_string("auction_data.json") {
            Ok(json_data) => {
                self.database = serde_json::from_str(&json_data)
                    .context("Failed to parse existing JSON file")?;
                info!("Loaded database with {} items from auction_data.json", self.database.len());
            }
            Err(_) => {
                info!("No existing database file found, starting fresh");
            }
        }
        Ok(())
    }

    // Main processing loop
    async fn run(&mut self) -> Result<()> {
        info!("Starting Hypixel SkyBlock Auction Tracker");

        // Load existing database
        self.load_database().await?;

        let mut interval = interval(Duration::from_secs(59));

        loop {
            interval.tick().await;

            info!("Checking for auction updates...");

            // Wait for API to update
            match self.wait_for_api_update().await {
                Ok(_) => {
                    // Fetch and process active auctions
                    match self.fetch_active_auctions().await {
                        Ok(auctions) => {
                            if !auctions.is_empty() {
                                self.process_active_auctions(auctions).await?;
                            }
                        }
                        Err(e) => {
                            error!("Failed to fetch active auctions: {}", e);
                            continue;
                        }
                    }

                    // Fetch and process ended auctions
                    match self.fetch_ended_auctions().await {
                        Ok(ended_auctions) => {
                            if !ended_auctions.is_empty() {
                                self.process_ended_auctions(ended_auctions).await?;

                                // Save database after processing ended auctions
                                self.save_database().await?;
                            }
                        }
                        Err(e) => {
                            error!("Failed to fetch ended auctions: {}", e);
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to check API updates: {}", e);
                    continue;
                }
            }
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();

    let mut tracker = AuctionTracker::new();

    // Handle Ctrl+C gracefully
    tokio::select! {
        result = tracker.run() => {
            if let Err(e) = result {
                error!("Application error: {}", e);
                return Err(e);
            }
        }
        _ = tokio::signal::ctrl_c() => {
            info!("Received Ctrl+C, saving database and shutting down...");
            if let Err(e) = tracker.save_database().await {
                error!("Failed to save database on shutdown: {}", e);
            }
        }
    }

    Ok(())
}
